import { IActivity } from "./activities.interface";
import { IHotel } from "./hotel.interface";
import { IUser } from "./user.interface";
import { IPackage } from "./package.interface";
import { IRoom } from "./room.interface";
import { IService } from "./service.interface";

export enum PaymentStatus {
  PARTIALLY_PAID = "partially-paid",
  FULLY_PAID = "fully-paid",
  UNPAID = "unpaid",
}

export interface IPackageInfo {
  package: IPackage;
  inclusions: string[];
  discount: number;
}

export interface IPaxDetails {
  adults: number;
  children: number;
  infants?: number;
}

export enum PaymentMethod {
  CASH = "cash",
  ONLINE = "online",
}

export enum BookingStatus {
  PENDING = "pending",
  CONFIRMED = "confirmed",
  CHECKED_IN = "checked-in",
  CHECKED_OUT = "checked-out",
  CANCELLED = "cancelled",
  NO_SHOW = "no-show",
}

export interface IBooking {
  _id: string;
  bookingId: string;
  reservationDate: Date;
  hotel: IHotel;
  room: IRoom;
  guest: IUser;
  checkIn: Date | string;
  checkOut: Date | string;
  expectedCheckOut: Date;
  package?: IPackage;
  paymentStatus: PaymentStatus;
  paymentMethod: PaymentMethod;
  amount: number;
  amountPaid: number;
  pax: IPaxDetails;
  specialRequests?: string;
  cancellationDate?: Date;
  cancellationReason?: string;
  status: BookingStatus;
  isConfirmed: boolean;
  durationNights: number;
  usedServices: {
    service: IService;
    price: number;
  }[];
  bookedActivities: IActivity[];
  orders: [];
}
