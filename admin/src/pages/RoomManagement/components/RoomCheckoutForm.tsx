import React, { useState } from "react";
import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import moment from "moment";
import { get } from "lodash";
import { IRoom, RoomStatus } from "../../../Interface/room.interface";
import { useUpdateRoom } from "../../../server-action/API/HotelConfiguration/room";
import { FormField } from "../../BookingManagement/components/ReservationCustomForm";
import { Icon } from "@iconify/react/dist/iconify.js";
import cross from "../../../assets/Svg/Cross.svg";

interface RoomCheckoutFormProps {
  room: IRoom;
  onClose: () => void;
  onSuccess?: () => void;
}

const RoomCheckoutValidationSchema = Yup.object().shape({
  actualCheckOut: Yup.string().required("Checkout date is required"),
  notes: Yup.string(),
});

const RoomCheckoutForm: React.FC<RoomCheckoutFormProps> = ({ room, onClose, onSuccess }) => {
  const [showWarning, setShowWarning] = useState(false);
  const updateRoomMutation = useUpdateRoom();

  const formik = useFormik({
    initialValues: {
      actualCheckOut: moment().format("YYYY-MM-DD"),
      notes: "",
    },
    validationSchema: RoomCheckoutValidationSchema,
    onSubmit: async (values) => {
      try {
        // Update room status to cleaning after checkout
        const roomUpdateData = {
          status: RoomStatus.CLEANING,
          lastCleaned: values.actualCheckOut,
          notes: values.notes,
        };

        await updateRoomMutation.mutateAsync({
          roomData: roomUpdateData,
          _id: room._id!,
        });

        onSuccess?.();
        onClose();
      } catch (error) {
        console.error("Error during room checkout:", error);
      }
    },
  });

  const handleForceCheckout = async () => {
    try {
      const roomUpdateData = {
        status: RoomStatus.CLEANING,
        lastCleaned: formik.values.actualCheckOut,
        notes: formik.values.notes,
      };

      await updateRoomMutation.mutateAsync({
        roomData: roomUpdateData,
        _id: room._id!,
      });

      onSuccess?.();
      onClose();
    } catch (error) {
      console.error("Error during force checkout:", error);
    }
  };

  const formFields = [
    {
      label: "Checkout Date",
      field: "actualCheckOut",
      type: "date",
      required: true,
    },
    {
      label: "Notes",
      field: "notes",
      type: "textarea",
      required: false,
    },
  ];

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 bg-[#EBFEF4]">
          <h2 className="text-xl font-semibold text-gray-900">
            Checkout Room {room.roomNo}
          </h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <img src={cross} alt="Close" className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Room Information */}
            <div>
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Room Summary</h2>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Room Number</span>
                    <span className="font-medium">{room.roomNo}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Room Type</span>
                    <span className="font-medium">{get(room, "roomType.name", "N/A")}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Current Status</span>
                    <span className="font-medium capitalize">{room.status}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Floor</span>
                    <span className="font-medium">{room.floor || "N/A"}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Base Price</span>
                    <span className="font-medium">Rs. {get(room, "roomPrice.base", 0).toLocaleString()}</span>
                  </div>

                  <div className="flex justify-between items-center pt-3 border-t-2 border-gray-300">
                    <span className="text-lg font-semibold text-gray-900">After Checkout</span>
                    <span className="text-lg font-bold text-blue-600">Cleaning Status</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Checkout Form */}
            <div>
              <FormikProvider value={formik}>
                <Form className="space-y-4">
                  {formFields.map((field) => (
                    <FormField
                      key={field.field}
                      label={field.label}
                      name={field.field}
                      type={field.type}
                      required={field.required}
                      formik={formik}
                    />
                  ))}

                  {/* Room Status Summary */}
                  <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Current Status:</span>
                      <span className="font-semibold capitalize">{room.status}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">After Checkout:</span>
                      <span className="font-semibold text-blue-600">Cleaning</span>
                    </div>
                    <div className="flex justify-between border-t pt-2">
                      <span className="text-gray-600">Next Available:</span>
                      <span className="font-semibold text-green-600">After Cleaning Complete</span>
                    </div>
                  </div>

                  {/* Warning Message */}
                  {showWarning && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <div className="flex items-start">
                        <Icon icon="mdi:warning" className="text-yellow-600 mt-0.5 mr-2" width="20" height="20" />
                        <div>
                          <h3 className="text-yellow-800 font-medium">Checkout Confirmation</h3>
                          <p className="text-yellow-700 text-sm mt-1">
                            Are you sure you want to checkout this room? The status will be changed to "Cleaning".
                          </p>
                          <div className="mt-3 flex gap-2">
                            <button
                              type="button"
                              onClick={handleForceCheckout}
                              className="bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700"
                              disabled={updateRoomMutation.isPending}
                            >
                              Proceed Anyway
                            </button>
                            <button
                              type="button"
                              onClick={() => setShowWarning(false)}
                              className="bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-400"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Info Message */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <Icon icon="mdi:information" className="text-blue-600 mt-0.5 mr-2" width="20" height="20" />
                      <div>
                        <h3 className="text-blue-800 font-medium">Checkout Information</h3>
                        <p className="text-blue-700 text-sm mt-1">
                          After checkout, the room status will be changed to "Cleaning" and will need to be cleaned before becoming available again.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-end gap-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-6 py-2 bg-[#6047E4] text-white rounded-md hover:bg-[#5038D3] disabled:opacity-50"
                      disabled={updateRoomMutation.isPending}
                    >
                      {updateRoomMutation.isPending ? "Processing..." : "Complete Checkout"}
                    </button>
                  </div>
                </Form>
              </FormikProvider>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoomCheckoutForm;
