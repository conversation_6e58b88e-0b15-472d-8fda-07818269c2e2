import { useState, useMemo } from "react";
import Header from "../../components/Header";
import MasterTable from "../../layouts/Table/MasterTable";
import { useGetAllRooms } from "../../server-action/API/HotelConfiguration/room";
import { PopupModal } from "../../components";
import { BillDetails } from "./component/BillDetails";
import { get } from "lodash";
import PaymentForm from "./component/PaymentForm";
import { Status } from "../../components/Status";
import RoomStatusFilter from "./RoomStatusFilter";

const RoomManagementIndex = () => {
  const { data: roomData, isLoading } = useGetAllRooms();
  const [showModal, setShowModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);

  // Filter state management
  const [filters, setFilters] = useState({
    search: "",
    status: null as string | null,
  });

  // Calculate status counts for the filter component
  const statusCounts = useMemo(() => {
    if (!roomData) return {};

    return roomData.reduce((acc: any, room: any) => {
      const status = get(room, "status", "unknown");
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});
  }, [roomData]);

  // Filter room data based on current filters
  const filteredRoomData = useMemo(() => {
    if (!roomData) return [];

    return roomData.filter((item: any) => {
      // Status filter
      if (filters.status && filters.status !== "all") {
        const itemStatus = get(item, "status", "").toLowerCase();
        if (itemStatus !== filters.status.toLowerCase()) {
          return false;
        }
      }

      // Search filter - search across multiple fields
      if (filters.search && filters.search.trim()) {
        const searchTerm = filters.search.toLowerCase().trim();
        const roomNo = get(item, "roomNo", "").toString().toLowerCase();
        const roomType = get(item, "roomType.name", "").toLowerCase();
        const price = get(item, "currentPrice", 0).toString();
        const bedCount = get(item, "beds.count", 0).toString();

        const searchFields = [roomNo, roomType, price, bedCount].join(" ");

        if (!searchFields.includes(searchTerm)) {
          return false;
        }
      }

      return true;
    });
  }, [roomData, filters]);

  // Handle filter changes from the custom filter component
  const handleFilterChange = (newFilters: {
    status: string | null;
    search: string;
  }) => {
    setFilters({
      status: newFilters.status,
      search: newFilters.search,
    });
  };

  // Table configuration
  const tableData = {
    column: [
      { title: "Room Category", key: "roomType" },
      { title: "Bed Capacity", key: "bed" },
      { title: "Room No.", key: "Room" },
      { title: "Price (NRS)", key: "price" },
      { title: "Facilities", key: "facility" },
      { title: "Room Status", key: "roomStatus" },
      { title: "Action", key: "action" },
    ],
    rows:
      filteredRoomData?.map((item: any) => ({
        id: get(item, "_id", ""),
        key: get(item, "_id", ""),
        roomType: get(item, "roomType.name", "N/A"),
        bed: get(item, "beds.count", 0),
        Room: get(item, "roomNo", "N/A"),
        price: `${get(item, "currentPrice", 0).toLocaleString()}`,
        facility: get(item, "amenities"),
        status: get(item, "status", ""),
        roomStatus: <Status status={item?.status} />,
        action: (
          <div className="flex justify-center">
            {get(item, "status", "") === "occupied" ? (
              <button
                className="px-4  text-white py-2 border border-[#000000] hover:bg-[#2A3A6D] hover:text-white bg-[#2A3A6D] rounded hover:border-[#2A3A6D] transition-colors duration-200 text-sm font-medium cursor-pointer"
                onClick={() => {
                  setSelectedItem(item);
                  setShowModal(true);
                }}
              >
                Checkout
              </button>
            ) : get(item, "status", "") === "available" ? (
              <button className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors duration-200 text-sm font-medium cursor-pointer">
                Check-in
              </button>
            ) : (
              "-"
            )}
          </div>
        ),
      })) ?? [],
  };

  // Calculate filtered results summary
  const filteredSummary = useMemo(() => {
    const total = roomData?.length || 0;
    const filtered = filteredRoomData.length;
    const isFiltered = filters.status || filters.search.trim();

    return {
      total,
      filtered,
      isFiltered,
      message: isFiltered
        ? `Showing ${filtered} of ${total} rooms`
        : `Showing all ${total} rooms`,
    };
  }, [roomData, filteredRoomData, filters]);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header showButton={false} />

      <div className="container mx-auto px-4 py-6">
        {/* Custom Filter Component */}
        <RoomStatusFilter
          onFilterChange={handleFilterChange}
          statusCounts={statusCounts}
        />

        {/* Results Summary */}
        <div className="mb-4 flex justify-between items-center">
          <div className="text-sm text-gray-600">{filteredSummary.message}</div>

          {/* Quick Stats */}
          <div className="flex gap-4 text-sm">
            <div className="text-green-600 font-medium">
              Available: {statusCounts.available || 0}
            </div>
            <div className="text-red-600 font-medium">
              Occupied: {statusCounts.occupied || 0}
            </div>
            <div className="text-yellow-600 font-medium">
              Reserved: {statusCounts.reserved || 0}
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="bg-white rounded-lg shadow-sm">
          <MasterTable
            columns={tableData.column}
            rows={tableData.rows ?? []}
            loading={isLoading}
            canSearch={false} // Disabled because we have custom search
            showFilter={false} // Disabled because we have custom filter
            sortBy="roomNo"
            sortOrder="asc"
          />
        </div>

        {/* No Results Message */}
        {!isLoading &&
          filteredRoomData.length === 0 &&
          roomData &&
          roomData.length > 0 && (
            <div className="text-center py-8 bg-white rounded-lg shadow-sm mt-4">
              <div className="text-gray-500">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400 mb-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.469-.786-6.172-2.109M12 21l3.09-6.26L12 9l-3.09 5.74L12 21z"
                  />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No rooms found
                </h3>
                <p className="text-gray-600">
                  Try adjusting your search criteria or filters to find what
                  you're looking for.
                </p>
              </div>
            </div>
          )}
      </div>

      {/* Checkout Modal */}
      {showModal && (
        <PopupModal
          onClose={() => {
            setShowModal(false);
            setSelectedItem(null);
          }}
          classname=""
        >
          <BillDetails
            roomData={selectedItem}
            setShowBillModal={setShowModal}
            setShowPaymentModal={setShowPaymentModal}
          />
        </PopupModal>
      )}

      {/* Payment Modal */}
      {showPaymentModal && (
        <PopupModal onClose={() => setShowPaymentModal(false)}>
          <PaymentForm onClose={() => setShowPaymentModal(false)} />
        </PopupModal>
      )}
    </div>
  );
};

export default RoomManagementIndex;
